import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { supabase } from '../lib/supabase';
import VenueCard from '../components/VenueCard';
import { Colors } from '../constants/Colors';
import { globalStyles } from '../constants/globalStyles';

export default function HomeScreen({ navigation }) {
  const [profile, setProfile] = useState(null);
  const [featuredVenues, setFeaturedVenues] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchHomeData();
  }, []);

  const fetchHomeData = async () => {
    try {
      // Get user profile
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        const { data: profileData } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();

        if (profileData) {
          setProfile(profileData);
        }
      }

      // Get featured venues (top rated)
      const { data: venuesData } = await supabase
        .from('venues')
        .select('*')
        .order('overall_rating', { ascending: false })
        .limit(3);

      if (venuesData) {
        setFeaturedVenues(venuesData);
      }
    } catch (error) {
      console.error('Error fetching home data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Günaydın';
    if (hour < 18) return 'İyi günler';
    return 'İyi akşamlar';
  };

  if (loading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" color={Colors.primary} />
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={[styles.greeting, globalStyles.heading]}>
          {getGreeting()}{profile?.username ? `, ${profile.username}` : ''}!
        </Text>
        <Text style={[styles.subtitle, globalStyles.body]}>
          Bugün hangi kahveyi keşfetmek istiyorsun?
        </Text>
      </View>

      <View style={styles.quickActions}>
        <TouchableOpacity
          style={styles.actionCard}
          onPress={() => navigation.navigate('Explore')}
        >
          <Ionicons name="compass" size={24} color={Colors.primary} />
          <Text style={[styles.actionText, globalStyles.bodySmall]}>Keşfet</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionCard}
          onPress={() => navigation.navigate('Search')}
        >
          <Ionicons name="search" size={24} color={Colors.primary} />
          <Text style={[styles.actionText, globalStyles.bodySmall]}>Ara</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.section}>
        <Text style={[styles.sectionTitle, globalStyles.subheading]}>Öne Çıkan Mekanlar</Text>
        {featuredVenues.map(venue => (
          <VenueCard key={venue.id} venue={venue} navigation={navigation} />
        ))}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
  },
  header: {
    padding: 20,
    paddingTop: 40,
  },
  greeting: {
    color: Colors.text,
    marginBottom: 8,
  },
  subtitle: {
    color: Colors.tabIconDefault,
    lineHeight: 24,
  },
  quickActions: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    gap: 16,
    marginBottom: 30,
  },
  actionCard: {
    flex: 1,
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    gap: 8,
  },
  actionText: {
    color: Colors.text,
    textAlign: 'center',
  },
  section: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  sectionTitle: {
    color: Colors.text,
    marginBottom: 16,
  },
});