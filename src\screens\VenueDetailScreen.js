import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ActivityIndicator, Modal, Button } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedScrollHandler,
  useAnimatedStyle,
  interpolate
} from 'react-native-reanimated';
import { supabase } from '../lib/supabase';
import ReviewCard from '../components/ReviewCard';
import AddReviewForm from '../components/AddReviewForm';
import EmptyState from '../components/EmptyState';
import { Colors } from '../constants/Colors';
import { globalStyles } from '../constants/globalStyles';
import { toast } from '../utils/toast';
import spacingScale from '../constants/spacing';

const HEADER_HEIGHT = 300;

export default function VenueDetailScreen({ route, navigation }) {
  const { venueId } = route.params;
  const [venue, setVenue] = useState(null);
  const [reviews, setReviews] = useState([]);
  const [loading, setLoading] = useState(true);
  const [modalVisible, setModalVisible] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);

  // Parallax animation
  const scrollY = useSharedValue(0);

  const scrollHandler = useAnimatedScrollHandler({
    onScroll: (event) => {
      scrollY.value = event.contentOffset.y;
    },
  });

  const headerAnimatedStyle = useAnimatedStyle(() => {
    const scale = interpolate(
      scrollY.value,
      [-100, 0, HEADER_HEIGHT],
      [1.2, 1, 0.8]
    );

    const opacity = interpolate(
      scrollY.value,
      [0, HEADER_HEIGHT / 2, HEADER_HEIGHT],
      [1, 0.5, 0]
    );

    return {
      transform: [{ scale }],
      opacity,
    };
  });

  useEffect(() => {
    const fetchVenueData = async () => {
      try {
        // Get current user
        const { data: { user } } = await supabase.auth.getUser();
        setCurrentUser(user);

        // Mekan detaylarını çek
        const { data: venueData, error: venueError } = await supabase
          .from('venues')
          .select('*')
          .eq('id', venueId)
          .single();

        if (!venueError && venueData) {
          setVenue(venueData);
          navigation.setOptions({ title: venueData.name });
        }

        // Yorumları çek - profiles tablosu ile join yaparak kullanıcı bilgilerini al
        const { data: reviewsData, error: reviewsError } = await supabase
          .from('reviews')
          .select(`
            *,
            profiles!reviews_user_id_fkey (
              username,
              full_name
            )
          `)
          .eq('venue_id', venueId)
          .order('created_at', { ascending: false });

        if (!reviewsError && reviewsData) {
          // Review data'yı author_name ile birleştir
          const processedReviews = reviewsData.map(review => ({
            ...review,
            author_name: review.profiles?.username || review.profiles?.full_name || review.author_name || 'Anonim Kullanıcı'
          }));
          setReviews(processedReviews);
        }
      } catch (error) {
        console.error('Error fetching venue data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchVenueData();
  }, [venueId]);

  const handleReviewSubmit = async ({ rating, content }) => {
    if (!currentUser) {
      toast.warning('Giriş Gerekli', 'Yorum yapmak için giriş yapmalısınız.');
      return;
    }

    try {
      // Get user profile for author name
      const { data: profileData } = await supabase
        .from('profiles')
        .select('username, full_name')
        .eq('id', currentUser.id)
        .single();

      const author_name = profileData?.username || profileData?.full_name || 'Kullanıcı';

      const { data, error } = await supabase
        .from('reviews')
        .insert([{
          venue_id: venueId,
          user_id: currentUser.id,
          rating,
          content,
          author_name
        }])
        .select(`
          *,
          profiles!reviews_user_id_fkey (
            username,
            full_name
          )
        `);
      
      if (!error && data) {
        // Process the new review and add to list
        const processedReview = {
          ...data[0],
          author_name: data[0].profiles?.username || data[0].profiles?.full_name || author_name
        };
        setReviews([processedReview, ...reviews]);
        setModalVisible(false);
        toast.success('Yorum Eklendi!', 'Yorumunuz başarıyla paylaşıldı.');
      } else {
        toast.error('Yorum Gönderilemedi', 'Bir hata oluştu. Lütfen tekrar deneyin.');
        console.error(error);
      }
    } catch (error) {
      toast.error('Beklenmeyen Hata', 'Yorum gönderilirken bir hata oluştu.');
      console.error(error);
    }
  };

  if (loading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" color={Colors.primary} />
      </View>
    );
  }

  if (!venue) {
    return (
      <View style={styles.centered}>
        <Text style={[styles.text, globalStyles.body]}>Mekan bulunamadı.</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Animated.ScrollView
        style={styles.scrollView}
        onScroll={scrollHandler}
        scrollEventThrottle={16}
      >
        <Animated.Image
          source={{ uri: venue.main_image_url || 'https://via.placeholder.com/400x200.png?text=No+Image' }}
          style={[styles.image, headerAnimatedStyle]}
        />
        <View style={styles.content}>
        <Text style={[styles.name, globalStyles.heading]}>{venue.name}</Text>
        <Text style={[styles.address, globalStyles.body]}>{venue.address}</Text>
        <Text style={[styles.rating, globalStyles.subheading]}>Puan: {venue.overall_rating.toFixed(1)} / 5.0</Text>
        
        <View style={styles.sectionHeader}>
          <Text style={[styles.sectionTitle, globalStyles.subheading]}>Yorumlar</Text>
          {currentUser && (
            <Button 
              title="Yorum Ekle" 
              onPress={() => setModalVisible(true)} 
              color={Colors.primary} 
            />
          )}
        </View>
        
        {reviews.length > 0 ? (
          reviews.map(review => (
            <ReviewCard key={review.id} review={review} />
          ))
        ) : (
          <EmptyState
            icon="chatbubble-outline"
            title="Henüz Yorum Yok"
            subtitle="Bu mekan hakkında ilk yorumu sen yapmaya ne dersin?"
            actionText={currentUser ? "İlk Yorumu Yap" : "Giriş Yap"}
            onActionPress={currentUser ? () => setModalVisible(true) : null}
            style={{ paddingVertical: spacingScale.xl }}
          />
        )}
      </View>

      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <AddReviewForm 
          onSubmit={handleReviewSubmit}
          onCancel={() => setModalVisible(false)}
        />
      </Modal>
      </Animated.ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background
  },
  scrollView: {
    flex: 1,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background
  },
  image: {
    width: '100%',
    height: HEADER_HEIGHT,
    resizeMode: 'cover',
  },
  content: { 
    padding: 20 
  },
  name: { 
    color: Colors.text, 
    marginBottom: 10 
  },
  address: { 
    color: Colors.secondary, 
    marginBottom: 20 
  },
  rating: { 
    color: Colors.primary
  },
  text: { 
    color: Colors.text
  },
  sectionHeader: { 
    flexDirection: 'row', 
    justifyContent: 'space-between', 
    alignItems: 'center', 
    marginTop: 30, 
    borderTopWidth: 1, 
    borderTopColor: '#444', 
    paddingTop: 20, 
    marginBottom: 20 
  },
  sectionTitle: { 
    color: Colors.text 
  },
  noReviewsText: { 
    color: Colors.tabIconDefault, 
    fontStyle: 'italic' 
  }
});