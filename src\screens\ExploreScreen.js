import React, { useState, useEffect } from 'react';
import { View, StyleSheet, FlatList } from 'react-native';
import Animated, { FadeInDown } from 'react-native-reanimated';
import { supabase } from '../lib/supabase';
import VenueCard from '../components/VenueCard';
import VenueCardSkeleton from '../components/VenueCardSkeleton';
import EmptyState from '../components/EmptyState';
import { Colors } from '../constants/Colors';
import spacingScale from '../constants/spacing';

export default function ExploreScreen({ navigation }) {
  const [venues, setVenues] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchVenues = async () => {
      try {
        setLoading(true);
        const { data, error: fetchError } = await supabase
          .from('venues')
          .select('*')
          .order('overall_rating', { ascending: false });

        if (fetchError) throw fetchError;
        
        setVenues(data || []);
      } catch (e) {
        setError(e.message);
      } finally {
        setLoading(false);
      }
    };

    fetchVenues();
  }, []);

  if (loading) {
    return (
      <View style={styles.container}>
        <FlatList
          data={[1, 2, 3, 4]} // Dummy data for skeleton loaders
          renderItem={() => <VenueCardSkeleton />}
          keyExtractor={(item, index) => `skeleton-${index}`}
          contentContainerStyle={{ paddingTop: 20, paddingBottom: 20 }}
          showsVerticalScrollIndicator={false}
        />
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.container}>
        <EmptyState
          icon="alert-circle-outline"
          title="Bir Hata Oluştu"
          subtitle={`Mekanlar yüklenirken hata: ${error}`}
          style={{ flex: 1 }}
        />
      </View>
    );
  }

  if (venues.length === 0 && !loading) {
    return (
      <View style={styles.container}>
        <EmptyState
          icon="storefront-outline"
          title="Henüz Mekan Yok"
          subtitle="Yakında harika kahve mekanları eklenecek!"
          style={{ flex: 1 }}
        />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Animated.FlatList
        data={venues}
        renderItem={({ item, index }) => (
          <Animated.View entering={FadeInDown.delay(index * 100)}>
            <VenueCard venue={item} navigation={navigation} />
          </Animated.View>
        )}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={{ paddingTop: spacingScale.md, paddingBottom: spacingScale.md }}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: { 
    flex: 1, 
    backgroundColor: Colors.background 
  },
  centered: { 
    flex: 1, 
    justifyContent: 'center', 
    alignItems: 'center', 
    backgroundColor: Colors.background 
  },
  errorText: { 
    color: 'red' 
  }
});