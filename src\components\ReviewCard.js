import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { globalStyles } from '../constants/globalStyles';

const StarRating = ({ rating }) => {
  const stars = [];
  for (let i = 1; i <= 5; i++) {
    stars.push(
      <Ionicons
        key={i}
        name={i <= rating ? 'star' : 'star-outline'}
        size={16}
        color="#FFD700"
      />
    );
  }
  return <View style={styles.starContainer}>{stars}</View>;
};

export default function ReviewCard({ review }) {
  return (
    <View style={styles.card}>
      <View style={styles.header}>
        <Text style={[styles.author, globalStyles.body]}>{review.author_name}</Text>
        <StarRating rating={review.rating} />
      </View>
      <Text style={[styles.content, globalStyles.bodySmall]}>{review.content}</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  card: { 
    backgroundColor: Colors.card, 
    borderRadius: 12, 
    padding: 16, 
    marginBottom: 15 
  },
  header: { 
    flexDirection: 'row', 
    justifyContent: 'space-between', 
    alignItems: 'center', 
    marginBottom: 10 
  },
  author: { 
    color: Colors.text,
    fontWeight: '600'
  },
  content: { 
    color: Colors.accent,
    lineHeight: 20 
  },
  starContainer: { 
    flexDirection: 'row' 
  },
});