{"name": "siptracker-clean", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo-google-fonts/poppins": "^0.4.0", "@expo/metro-runtime": "~5.0.4", "@react-native-community/slider": "^4.5.7", "@react-navigation/bottom-tabs": "^7.3.14", "@react-navigation/native": "^7.1.10", "@react-navigation/native-stack": "^7.3.14", "@supabase/supabase-js": "^2.50.0", "d3-shape": "^3.2.0", "expo": "~53.0.10", "expo-font": "~13.3.1", "expo-linear-gradient": "^14.1.5", "expo-status-bar": "~2.2.3", "lottie-react-native": "^7.2.2", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.3", "react-native-dotenv": "^3.4.11", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "^7.2.1", "react-native-svg-charts": "^5.4.0", "react-native-toast-message": "^2.3.0", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}