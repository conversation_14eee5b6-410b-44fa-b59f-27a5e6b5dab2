import React, { useState } from 'react';
import { View, Text, TextInput, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { supabase } from '../lib/supabase';
import { Colors } from '../constants/Colors';
import { globalStyles } from '../constants/globalStyles';
import { toast } from '../utils/toast';
import spacingScale from '../constants/spacing';

export default function LoginScreen({ navigation }) {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);

  const getErrorMessage = (error) => {
    switch (error.message) {
      case 'Invalid login credentials':
        return 'E-posta veya şifre hatalı. Lütfen kontrol edin.';
      case 'Email not confirmed':
        return 'E-posta adresinizi doğrulamanız gerekiyor. Gelen kutunuzu kontrol edin.';
      case 'Too many requests':
        return 'Çok fazla deneme yaptınız. Lütfen birkaç dakika bekleyin.';
      case 'User not found':
        return 'Bu e-posta adresi ile kayıtlı kullanıcı bulunamadı.';
      default:
        return `Giriş hatası: ${error.message}`;
    }
  };

  const handleLogin = async () => {
    if (!email || !password) {
      toast.warning('Eksik Bilgi', 'Lütfen e-posta ve şifrenizi girin.');
      return;
    }

    if (!email.includes('@')) {
      toast.error('Geçersiz E-posta', 'Lütfen geçerli bir e-posta adresi girin.');
      return;
    }

    setLoading(true);
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: email.toLowerCase().trim(),
        password,
      });

      if (error) {
        toast.error('Giriş Başarısız', getErrorMessage(error));
      } else {
        // Başarılı giriş - navigation auth listener tarafından yapılacak
        toast.success('Hoş Geldiniz!', 'Başarıyla giriş yaptınız.');
        console.log('Login successful for user:', data.user?.email);
      }
    } catch (error) {
      console.error('Unexpected login error:', error);
      toast.error('Beklenmeyen Hata', 'Lütfen internet bağlantınızı kontrol edin ve tekrar deneyin.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <Text style={[styles.title, globalStyles.heading]}>Hoş Geldiniz</Text>
        <Text style={[styles.subtitle, globalStyles.body]}>
          Kahve keşif yolculuğunuza devam etmek için giriş yapın
        </Text>

        <View style={styles.form}>
          <TextInput
            style={[styles.input, globalStyles.body]}
            placeholder="E-posta"
            placeholderTextColor={Colors.tabIconDefault}
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            autoCapitalize="none"
            autoComplete="email"
          />

          <TextInput
            style={[styles.input, globalStyles.body]}
            placeholder="Şifre"
            placeholderTextColor={Colors.tabIconDefault}
            value={password}
            onChangeText={setPassword}
            secureTextEntry
            autoCapitalize="none"
          />

          <TouchableOpacity 
            style={[styles.button, loading && styles.buttonDisabled]}
            onPress={handleLogin}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator color={Colors.text} />
            ) : (
              <Text style={[styles.buttonText, globalStyles.body]}>Giriş Yap</Text>
            )}
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.linkButton}
            onPress={() => navigation.navigate('Register')}
          >
            <Text style={[styles.linkText, globalStyles.bodySmall]}>
              Hesabınız yok mu? Kayıt olun
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: 32,
  },
  title: {
    color: Colors.text,
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    color: Colors.tabIconDefault,
    textAlign: 'center',
    marginBottom: 40,
    lineHeight: 24,
  },
  form: {
    gap: 16,
  },
  input: {
    backgroundColor: Colors.card,
    color: Colors.text,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  button: {
    backgroundColor: Colors.primary,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginTop: 8,
  },
  buttonDisabled: {
    opacity: 0.7,
  },
  buttonText: {
    color: Colors.text,
    fontWeight: '600',
  },
  linkButton: {
    paddingVertical: 12,
    alignItems: 'center',
  },
  linkText: {
    color: Colors.primary,
  },
});