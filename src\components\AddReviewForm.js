import React, { useState } from 'react';
import { View, Text, TextInput, StyleSheet, TouchableOpacity, Button } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { globalStyles } from '../constants/globalStyles';

export default function AddReviewForm({ onSubmit, onCancel }) {
  const [rating, setRating] = useState(0);
  const [content, setContent] = useState('');

  const Star = ({ filled, onPress }) => (
    <TouchableOpacity onPress={onPress}>
      <Ionicons 
        name={filled ? 'star' : 'star-outline'} 
        size={32} 
        color="#FFD700" 
        style={{ marginRight: 10 }} 
      />
    </TouchableOpacity>
  );

  const handleSubmit = () => {
    if (rating > 0 && content.length > 2) {
      onSubmit({ rating, content });
    } else {
      alert('Lütfen puan verin ve en az 3 karakterlik bir yorum yazın.');
    }
  };

  return (
    <View style={styles.modalContainer}>
      <View style={styles.modalContent}>
        <Text style={[styles.title, globalStyles.subheading]}>Yorumunu Paylaş</Text>
        <View style={styles.starContainer}>
          {[1, 2, 3, 4, 5].map(i => (
            <Star 
              key={i} 
              filled={i <= rating} 
              onPress={() => setRating(i)} 
            />
          ))}
        </View>
        <TextInput
          style={[styles.input, globalStyles.body]}
          placeholder="Mekan hakkındaki düşüncelerin..."
          placeholderTextColor="#888"
          multiline
          value={content}
          onChangeText={setContent}
        />
        <Button 
          title="Yorumu Gönder" 
          onPress={handleSubmit} 
          color={Colors.primary} 
        />
        <TouchableOpacity onPress={onCancel} style={styles.cancelButton}>
          <Text style={[styles.cancelText, globalStyles.bodySmall]}>İptal</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  modalContainer: { 
    flex: 1, 
    justifyContent: 'center', 
    alignItems: 'center', 
    backgroundColor: 'rgba(0,0,0,0.7)' 
  },
  modalContent: { 
    backgroundColor: Colors.card, 
    padding: 20, 
    borderRadius: 10, 
    width: '90%', 
    alignItems: 'center' 
  },
  title: { 
    color: Colors.text, 
    marginBottom: 20 
  },
  starContainer: { 
    flexDirection: 'row', 
    marginBottom: 20 
  },
  input: { 
    backgroundColor: Colors.background, 
    color: Colors.text, 
    borderRadius: 8, 
    width: '100%', 
    height: 100, 
    padding: 10, 
    textAlignVertical: 'top', 
    marginBottom: 20 
  },
  cancelButton: { 
    marginTop: 10 
  },
  cancelText: { 
    color: Colors.tabIconDefault 
  }
});